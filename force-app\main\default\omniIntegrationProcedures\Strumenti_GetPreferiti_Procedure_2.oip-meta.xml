<?xml version="1.0" encoding="UTF-8"?>
<OmniIntegrationProcedure xmlns="http://soap.sforce.com/2006/04/metadata">
    <customJavaScript>{}</customJavaScript>
    <elementTypeComponentMapping>{&quot;ElementTypeToHTMLTemplateList&quot;:[]}</elementTypeComponentMapping>
    <isActive>true</isActive>
    <isIntegProcdSignatureAvl>false</isIntegProcdSignatureAvl>
    <isIntegrationProcedure>true</isIntegrationProcedure>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <isMetadataCacheDisabled>false</isMetadataCacheDisabled>
    <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
    <isTestProcedure>false</isTestProcedure>
    <isWebCompEnabled>false</isWebCompEnabled>
    <language>Procedure</language>
    <name>GetStrumentiPreferiti</name>
    <omniProcessElements>
        <childElements>
            <isActive>true</isActive>
            <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
            <level>1.0</level>
            <name>Response Error</name>
            <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
            <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;fiscalCode&quot; : &quot;=GetUserData:User|1:FederationIdentifier&quot;,
    &quot;listSize&quot; : &quot;=0&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
            <sequenceNumber>1.0</sequenceNumber>
            <type>Response Action</type>
        </childElements>
        <description>Conditional Block</description>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Conditional Block</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;TransformRemoteResponse:error == \&quot;No input objects sent.\&quot;&quot;,
  &quot;id&quot; : &quot;&quot;,
  &quot;internalNotes&quot; : &quot;&quot;,
  &quot;isActive&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;isIfElseBlock&quot; : false
}</propertySetConfig>
        <sequenceNumber>4.0</sequenceNumber>
        <type>Conditional Block</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>GetUserData</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;dataRaptor Input Parameters&quot; : [ ],
  &quot;show&quot; : null,
  &quot;failOnStepError&quot; : true,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureResponse&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : {
    &quot;UserID&quot; : &quot;=$Vlocity.UserId&quot;
  },
  &quot;bundle&quot; : &quot;GetUserData&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>1.0</sequenceNumber>
        <type>DataRaptor Turbo Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>RemoteAction</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failOnStepError&quot; : true,
  &quot;useFormulas&quot; : true,
  &quot;additionalInput&quot; : { },
  &quot;additionalOutput&quot; : { },
  &quot;failureResponse&quot; : { },
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;,
  &quot;remoteClass&quot; : &quot;MenuStrumentiOmnistudio&quot;,
  &quot;remoteOptions&quot; : { },
  &quot;remoteMethod&quot; : &quot;getPreferiti&quot;,
  &quot;chainOnStep&quot; : false,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalChainableResponse&quot; : { },
  &quot;show&quot; : null,
  &quot;isActive&quot; : true,
  &quot;disOnTplt&quot; : false
}</propertySetConfig>
        <sequenceNumber>2.0</sequenceNumber>
        <type>Remote Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>Response</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;vlcResponseHeaders&quot; : { },
  &quot;returnFullDataJSON&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : true,
  &quot;responseFormat&quot; : &quot;JSON&quot;,
  &quot;isActive&quot; : true,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : {
    &quot;fiscalCode&quot; : &quot;=GetUserData:User|1:FederationIdentifier&quot;,
    &quot;listSize&quot; : &quot;=LISTSIZE(TransformRemoteResponse)&quot;,
    &quot;preferiti&quot; : &quot;=LIST(TransformRemoteResponse)&quot;
  },
  &quot;sendJSONPath&quot; : &quot;&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;id&quot; : &quot;&quot;,
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>5.0</sequenceNumber>
        <type>Response Action</type>
    </omniProcessElements>
    <omniProcessElements>
        <isActive>true</isActive>
        <isOmniScriptEmbeddable>false</isOmniScriptEmbeddable>
        <level>0.0</level>
        <name>TransformRemoteResponse</name>
        <omniProcessVersionNumber>0.0</omniProcessVersionNumber>
        <propertySetConfig>{
  &quot;sendOnlyAdditionalInput&quot; : false,
  &quot;returnOnlyAdditionalOutput&quot; : false,
  &quot;failOnStepError&quot; : false,
  &quot;isActive&quot; : true,
  &quot;chainOnStep&quot; : false,
  &quot;returnOnlyFailureResponse&quot; : false,
  &quot;ignoreCache&quot; : false,
  &quot;responseJSONPath&quot; : &quot;&quot;,
  &quot;responseJSONNode&quot; : &quot;&quot;,
  &quot;executionConditionalFormula&quot; : &quot;&quot;,
  &quot;actionMessage&quot; : &quot;&quot;,
  &quot;additionalOutput&quot; : { },
  &quot;sendJSONPath&quot; : &quot;RemoteAction:result&quot;,
  &quot;disOnTplt&quot; : false,
  &quot;failureConditionalFormula&quot; : &quot;&quot;,
  &quot;failureResponse&quot; : { },
  &quot;id&quot; : &quot;&quot;,
  &quot;additionalInput&quot; : { },
  &quot;useFormulas&quot; : true,
  &quot;restOptions&quot; : { },
  &quot;bundle&quot; : &quot;TransformPreferitiStructure&quot;,
  &quot;sendJSONNode&quot; : &quot;&quot;
}</propertySetConfig>
        <sequenceNumber>3.0</sequenceNumber>
        <type>DataRaptor Transform Action</type>
    </omniProcessElements>
    <omniProcessKey>Strumenti_GetPreferiti</omniProcessKey>
    <omniProcessType>Integration Procedure</omniProcessType>
    <propertySetConfig>{
  &quot;linkToExternalObject&quot; : &quot;&quot;,
  &quot;trackingCustomData&quot; : { },
  &quot;includeAllActionsInResponse&quot; : false,
  &quot;columnsPropertyMap&quot; : [ ],
  &quot;relationshipFieldsMap&quot; : [ ],
  &quot;labelSingular&quot; : &quot;&quot;,
  &quot;labelPlural&quot; : &quot;&quot;,
  &quot;description&quot; : &quot;&quot;,
  &quot;nameColumn&quot; : &quot;&quot;,
  &quot;rollbackOnError&quot; : false,
  &quot;chainableQueriesLimit&quot; : 50,
  &quot;chainableDMLStatementsLimit&quot; : null,
  &quot;chainableCpuLimit&quot; : 2000,
  &quot;chainableHeapSizeLimit&quot; : null,
  &quot;chainableDMLRowsLimit&quot; : null,
  &quot;chainableQueryRowsLimit&quot; : null,
  &quot;chainableSoslQueriesLimit&quot; : null,
  &quot;chainableActualTimeLimit&quot; : null,
  &quot;additionalChainableResponse&quot; : { },
  &quot;queueableChainableQueriesLimit&quot; : 120,
  &quot;queueableChainableCpuLimit&quot; : 40000,
  &quot;queueableChainableHeapSizeLimit&quot; : 6,
  &quot;ttlMinutes&quot; : 5,
  &quot;mockResponseMap&quot; : { },
  &quot;transientValues&quot; : {
    &quot;deactivateConsent&quot; : false
  }
}</propertySetConfig>
    <subType>GetPreferiti</subType>
    <type>Strumenti</type>
    <uniqueName>Strumenti_GetPreferiti_Procedure_2</uniqueName>
    <versionNumber>2.0</versionNumber>
    <webComponentKey>08f6a93b-b280-94e3-0566-8da27b807fc5</webComponentKey>
</OmniIntegrationProcedure>
