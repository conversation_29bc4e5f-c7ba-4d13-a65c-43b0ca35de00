<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;vlcTimeTracking&quot; : {
    &quot;getPermissionSetName&quot; : null,
    &quot;getUserFederationIdentifier&quot; : null
  },
  &quot;vlcPersistentComponent&quot; : { },
  &quot;userTimeZone&quot; : null,
  &quot;userProfile&quot; : null,
  &quot;userName&quot; : null,
  &quot;userId&quot; : null,
  &quot;timeStamp&quot; : null,
  &quot;ContextId&quot; : null
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>UniGetFeiRequestPayload</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem7</globalKey>
        <inputFieldName>userId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>userId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem6</globalKey>
        <inputFieldName>contentVersion:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>idDoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem5</globalKey>
        <inputFieldName>contentVersion:RecordType.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>tipoDoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem4</globalKey>
        <inputFieldName>accDetails:SourceSystemIdentifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ciuid</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| NOW</formulaConverted>
        <formulaExpression>NOW()</formulaExpression>
        <formulaResultPath>timestamp</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem3</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>contentDocLink:LinkedEntityId</filterValue>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem2</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>AccountDetails__c</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accDetails</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>contentVersion:ContentDocumentId</filterValue>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem1</globalKey>
        <inputFieldName>ContentDocumentId</inputFieldName>
        <inputObjectName>ContentDocumentLink</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentDocLink</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>ContentVersionId</filterValue>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem0</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>ContentVersion</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>contentVersion</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>anagrafica</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem9</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>ambito</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniGetFeiRequestPayloadCustom0jI9O000000u0ADUAYItem8</globalKey>
        <inputFieldName>timestamp</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniGetFeiRequestPayload</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>timestamp</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;ContentVersionId&quot; : &quot;0689O00000CtIewQAF&quot;,
  &quot;userId&quot; : &quot;0057Q00000A8CyeQAF&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>UniGetFeiRequestPayload_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
