global without sharing class MenuStrumentiOmnistudio implements System.Callable {
    
    private static final String GENERIC_ERROR = 'Si è verificato un errore';

    public Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName.equals('getPreferiti')) {
                getPreferiti(inputs, output);
            } else {
                output.put('success', false);
                output.put('errorMessage', 'Metodo non riconosciuto: ' + methodName);
                result = false;
            }

        } catch (Exception e) {
            output.put('success', false);
            output.put('errorMessage', GENERIC_ERROR);
            result = false;
        }

        return result;
    }

    private void getPreferiti(Map<String, Object> inputs, Map<String, Object> output) {

        Id assetRtId = Schema.SObjectType.Asset.getRecordTypeInfosByDeveloperName().get('User').getRecordTypeId();

        List<Asset> assetList = [
            SELECT User__c,
                Category__c,
                Value__c,
                Key__c,
                Fei_Parameters__c,
                Fei_Link__c,
                Status
            FROM   Asset
            WHERE  User__c = :UserInfo.getUserId()
            AND    Category__c = 'Preferiti'
            AND    RecordTypeId = :assetRtId
            AND    Status = 'Registered'
        ];

        if (assetList == null || assetList.isEmpty()) {
            output.put('success', true);
            output.put('result', new List<Map<String, Object>>());
            return;
        }

        // raccogliamo i developerName dai preferiti su Asset
        Set<String> apiNameString = new Set<String>();
        Map<String, Asset> assetByKey = new Map<String, Asset>();
        for (Asset ast : assetList) {
            apiNameString.add(ast.Key__c);
            assetByKey.put(ast.Key__c, ast);
        }

        // prendo i metadati esistenti
        List<Menu_Strumenti_Tree_Structure__mdt> rows = [
            SELECT MasterLabel,
                DeveloperName,
                Is_Active__c,
                Parent__c,
                Context__c,
                Section__c,
                Order__c,
                Type__c,
                FEI_ID__c,
                Redirect_Link__c,
                Redirect_Type__c,
                Profile_Code__c
            FROM   Menu_Strumenti_Tree_Structure__mdt
            WHERE  DeveloperName IN :apiNameString
        ];

        // Map di metadata per accesso rapido
        Map<String, Menu_Strumenti_Tree_Structure__mdt> metaByDev = new Map<String, Menu_Strumenti_Tree_Structure__mdt>();
        for (Menu_Strumenti_Tree_Structure__mdt row : rows) {
            metaByDev.put(row.DeveloperName, row);
        }

        // 4) Costruisco FEI uniformi
        List<Map<String, Object>> result = new List<Map<String, Object>>();
        for (String devName : apiNameString) {
            if (metaByDev.containsKey(devName)) {
                // esiste nel metadata costruisco da metadata
                Menu_Strumenti_Tree_Structure__mdt m = metaByDev.get(devName);
                Map<String, Object> obj = new Map<String, Object>();
                obj.put('masterLabel', m.MasterLabel);
                obj.put('developerName', m.DeveloperName);
                obj.put('feiId', m.FEI_ID__c);
                obj.put('type', m.Type__c);
                obj.put('redirectLink', m.Redirect_Link__c);
                obj.put('redirectType', m.Redirect_Type__c);
                obj.put('params', safeParams(assetByKey.get(devName).Fei_Parameters__c));
                result.add(obj);
            } else {
                // NON esiste nel metadata costruisco dal record Asset
                Asset ast = assetByKey.get(devName);
                Map<String, Object> obj = new Map<String, Object>();
                obj.put('masterLabel', ast.Value__c);
                obj.put('developerName', ast.Key__c);
                obj.put('redirectLink', ast.Fei_Link__c);
                obj.put('params', safeParams(ast.Fei_Parameters__c));
                obj.put('type', 'FEI');
                result.add(obj);
            }
        }

        output.put('success', true);
        output.put('result', result);
    }

    private static Object safeParams(String raw) {
    if (String.isBlank(raw)) return null;
        try {
            return JSON.deserializeUntyped(raw); // torna Map<String,Object> o List<Object>
        } catch (Exception e) {
            return raw; // fallback: lo lasci come stringa
        }
    }
}