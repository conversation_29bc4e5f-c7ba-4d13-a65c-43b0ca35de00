<?xml version="1.0" encoding="UTF-8"?>
<CustomApplication xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Account_Account_Relationship_for_KPI_Bank</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>FinServ__AccountAccountRelation__c</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Case_Record_Page1</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Case</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Event_Record_Page1</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Event</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Insurance_Policy_Record_Page1</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>InsurancePolicy</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Service_Resource_Record_Page1</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>ServiceResource</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>Service_Territory_Record_Page</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>ServiceTerritory</pageOrSobjectType>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <content>Home_SA</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
        <pageOrSobjectType>standard-home</pageOrSobjectType>
    </actionOverrides>
    <brand>
        <headerColor>#1C3A44</headerColor>
        <logo>Unipol_Gruppo_Logosvg1</logo>
        <logoVersion>1</logoVersion>
        <shouldOverrideOrgTheme>false</shouldOverrideOrgTheme>
    </brand>
    <formFactors>Large</formFactors>
    <isNavAutoTempTabsDisabled>false</isNavAutoTempTabsDisabled>
    <isNavPersonalizationDisabled>false</isNavPersonalizationDisabled>
    <isNavTabPersistenceDisabled>false</isNavTabPersistenceDisabled>
    <isOmniPinnedViewEnabled>false</isOmniPinnedViewEnabled>
    <label>Unipol CRM Agenzia</label>
    <navType>Console</navType>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Unipol Responsabile User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Agenziale</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Agenziale</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Agenziale</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Agenziale</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_IN</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_IN</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_IN</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_IN</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_OUT</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_OUT</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_OUT</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.BPER_OUT</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Cabina_di_Regia_BPER</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Cabina_di_Regia_BPER</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Cabina_di_Regia_BPER</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Cabina_di_Regia_BPER</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__General</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__General</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__General</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__General</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.InterestShow</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Omnicanale</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Omnicanale</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Omnicanale</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Omnicanale</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__WalletShareOpportunity</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__WalletShareOpportunity</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__WalletShareOpportunity</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__WalletShareOpportunity</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Prodotto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Prodotto</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Prodotto</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Prodotto</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__RetirementPlanning</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__RetirementPlanning</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__RetirementPlanning</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.FinServ__RetirementPlanning</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Segnalazione_BPER</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Segnalazione_BPER</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Segnalazione_BPER</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.Segnalazione_BPER</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Customer Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>FSC Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>FSC Associate Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>FSC Mortgage Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>FSC Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>FSC Wealth Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Identity User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - API Only Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - Salesforce</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Partner Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Partner Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Sales Insights Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Salesforce API Only System Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Service Agent</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>ServiceCloud</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Service Cloud User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Service Supervisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Service Cloud User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Service Supervisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>ServiceCloud</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Service Agent</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Salesforce API Only System Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Sales Insights Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Partner Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Partner Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - Salesforce</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - API Only Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Identity User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>FSC Wealth Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>FSC Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>FSC Mortgage Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>FSC Associate Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>FSC Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Customer Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.Prospect</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Service Cloud User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Service Supervisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>ServiceCloud</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Service Agent</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Salesforce API Only System Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Sales Insights Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Partner Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Partner Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - Salesforce</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - API Only Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Identity User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>FSC Wealth Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>FSC Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>FSC Mortgage Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>FSC Associate Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>FSC Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Customer Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>PersonAccount.PersonAccount</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Service Cloud User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Service Supervisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>ServiceCloud</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Service Agent</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Salesforce API Only System Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Sales Insights Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Partner Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Partner Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - Salesforce</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - API Only Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Identity User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>FSC Wealth Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>FSC Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>FSC Mortgage Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>FSC Associate Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>FSC Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Customer Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Individual_Record_Page2</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesIndividual</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.InterestShow</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.InterestShow</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>OpportunityRecordPage</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Opportunity</pageOrSobjectType>
        <recordType>Opportunity.InterestShow</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Analytics Cloud Security User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Chatter External User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Chatter Free User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Chatter Moderator User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>ContractManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Customer Community Plus User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Customer Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>FSC Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>FSC Associate Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>FSC Mortgage Officer</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>FSC Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>FSC Wealth Advisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Guest License User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Identity User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>MarketingProfile</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - API Only Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Minimum Access - Salesforce</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Partner Community Login User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Partner Community User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Personal Banker</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Read Only</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Relationship Manager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Sales Insights Integration User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Salesforce API Only System Integrations</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Service Agent</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>ServiceCloud</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Service Cloud User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Service Supervisor</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>SolutionManager</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Standard</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Admin</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Unipol Admin Light</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Responsabile_Agenzia</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>ServiceResource</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Responsabile User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Unipol_Event_Record_Page</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Event</pageOrSobjectType>
        <type>Flexipage</type>
        <profile>Unipol Standard User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>Business_Record_Page21</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>Account</pageOrSobjectType>
        <recordType>Account.FinServ__IndustriesBusiness</recordType>
        <type>Flexipage</type>
        <profile>Unipol Responsabile User</profile>
    </profileActionOverrides>
    <profileActionOverrides>
        <actionName>View</actionName>
        <content>AccountDetails1</content>
        <formFactor>Large</formFactor>
        <pageOrSobjectType>AccountDetails__c</pageOrSobjectType>
        <recordType>AccountDetails__c.Business</recordType>
        <type>Flexipage</type>
        <profile>Unipol Responsabile User</profile>
    </profileActionOverrides>
    <tabs>Menu_Strumenti</tabs>
    <tabs>ahdnow_redirect</tabs>
    <tabs>standard-Opportunity</tabs>
    <tabs>standard-Event</tabs>
    <tabs>standard-Case</tabs>
    <uiType>Lightning</uiType>
    <utilityBar>Unipol_Scheda_Soggetti_UtilityBar</utilityBar>
    <workspaceConfig>
        <mappings>
            <tab>Menu_Strumenti</tab>
        </mappings>
        <mappings>
            <tab>ahdnow_redirect</tab>
        </mappings>
        <mappings>
            <tab>standard-Case</tab>
        </mappings>
        <mappings>
            <tab>standard-Event</tab>
        </mappings>
        <mappings>
            <fieldName>AccountId</fieldName>
            <tab>standard-Opportunity</tab>
        </mappings>
    </workspaceConfig>
</CustomApplication>
