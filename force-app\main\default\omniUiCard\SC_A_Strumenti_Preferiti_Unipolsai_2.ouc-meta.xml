<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\t\t\t\&quot;type\&quot;: \&quot;FEI\&quot;,\n\t\t\t\&quot;label\&quot;: \&quot;Assestment rischio Cyber\&quot;,\n\t\t\t\&quot;apiName\&quot;: \&quot;UN_AF_ASSESTMENTRISCHIOCYBER\&quot;,\n\t\t\t\&quot;feiId\&quot;: \&quot;ASSESTMENT.RISCHIO.CYBER\&quot;\n\t\t}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>SC_A_Strumenti_Preferiti</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;none&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;\\{label}&quot;,&quot;iconName&quot;:&quot;utility:new_window&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753192472506&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;customLwc&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;flyoutLwc&quot;:&quot;feiContainer&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-95&quot;,&quot;field&quot;:&quot;type&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;FEI&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]},&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;feiid&quot;:&quot;{feiId}&quot;,&quot;fiscalcode&quot;:&quot;{fiscalCode}&quot;,&quot;feiRequestPayload&quot;:&quot;{params}&quot;}},&quot;key&quot;:&quot;1752681351810-7ta8wneng&quot;,&quot;label&quot;:&quot;FEI&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0},{&quot;key&quot;:&quot;1752681368330-txhr96ecz&quot;,&quot;label&quot;:&quot;REDIRECT INTERNO TAB&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:false,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752755933104&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Navigation Item&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;type&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;REDIRECT INTERNO&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-13&quot;,&quot;field&quot;:&quot;redirectType&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;standard__navItemPage&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;&amp;&amp;&quot;}]},&quot;Navigation Item&quot;:{&quot;targetName&quot;:&quot;{redirect}&quot;}},&quot;actionIndex&quot;:1},{&quot;key&quot;:&quot;1753086280182-uankxfw1e&quot;,&quot;label&quot;:&quot;REDIRECT ESTERNO&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1753086306333&quot;,&quot;type&quot;:&quot;Custom&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;New Tab/Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;{redirectLink}&quot;},&quot;actionConditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-0&quot;,&quot;field&quot;:&quot;type&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;REDIRECT ESTERNO&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false}]}},&quot;actionIndex&quot;:2}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:false,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;},&quot;flyoutChannel&quot;:&quot;&quot;,&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}},&quot;ariaLabel&quot;:&quot;\\{label}&quot;,&quot;iconSize&quot;:&quot;x-small&quot;,&quot;iconColor&quot;:&quot;#747474&quot;,&quot;iconOnly&quot;:false},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}},&quot;iconSize&quot;:&quot;x-small&quot;,&quot;iconColor&quot;:&quot;#747474&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;class&quot;:&quot; slds-p-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;elementLabel&quot;:&quot;Action-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;xxx-small&quot;,&quot;label&quot;:&quot;top:xxx-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;,&quot;large&quot;:&quot;12&quot;,&quot;medium&quot;:&quot;12&quot;,&quot;small&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#cccccc&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{&quot;styles&quot;:{&quot;label&quot;:{&quot;textAlign&quot;:&quot;left&quot;}},&quot;iconSize&quot;:&quot;x-small&quot;,&quot;iconColor&quot;:&quot;#747474&quot;},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;,&quot;class&quot;:&quot; slds-p-top_xxx-small &quot;,&quot;style&quot;:&quot;      \n         cursor: pointer;\nfont-weight: bold;\ncolor: #3598db;\ntext-decoration: underline;\nmargin-bottom: -5px;&quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;uKey&quot;:&quot;1753268794068-730&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;utility:new_window&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon__svg--default&quot;,&quot;variant&quot;:&quot;default&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_9-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;9&quot;}},&quot;elementLabel&quot;:&quot;Icon-1&quot;,&quot;uKey&quot;:&quot;1753268794068-743&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;Custom&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;body&quot;:&quot;{\n\t\t\t\&quot;type\&quot;: \&quot;FEI\&quot;,\n\t\t\t\&quot;label\&quot;: \&quot;Assestment rischio Cyber\&quot;,\n\t\t\t\&quot;apiName\&quot;: \&quot;UN_AF_ASSESTMENTRISCHIOCYBER\&quot;,\n\t\t\t\&quot;feiId\&quot;: \&quot;ASSESTMENT.RISCHIO.CYBER\&quot;\n\t\t}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[]},&quot;title&quot;:&quot;SC_A_Strumenti_Preferiti&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSC_A_StrumentiModaleRinnovi_1_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003cye9SAA&quot;,&quot;MasterLabel&quot;:&quot;cfSC_A_StrumentiModaleRinnovi_1_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;uniqueKey&quot;:&quot;{apiName}&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;SC_A_Strumenti_Preferiti&quot;,&quot;isExposed&quot;:true,&quot;apiVersion&quot;:56},&quot;listenToWidthResize&quot;:false,&quot;osSupport&quot;:false,&quot;jsonEscapeSupport&quot;:false}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;type&quot;:&quot;FEI&quot;,&quot;label&quot;:&quot;Assestment rischio Cyber&quot;,&quot;apiName&quot;:&quot;UN_AF_ASSESTMENTRISCHIOCYBER&quot;,&quot;feiId&quot;:&quot;ASSESTMENT.RISCHIO.CYBER&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>2</versionNumber>
</OmniUiCard>
