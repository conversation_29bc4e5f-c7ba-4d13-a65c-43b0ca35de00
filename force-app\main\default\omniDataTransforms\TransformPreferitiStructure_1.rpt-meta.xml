<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedOutputJson>{
  &quot;type&quot; : &quot;Text&quot;,
  &quot;feiId&quot; : &quot;Text&quot;,
  &quot;redirect&quot; : &quot;Text&quot;,
  &quot;redirectType&quot; : &quot;Text&quot;
}</expectedOutputJson>
    <fieldLevelSecurityEnabled>true</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>TransformPreferitiStructure</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom1410</globalKey>
        <inputFieldName>params</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>params</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom5884</globalKey>
        <inputFieldName>masterLabel</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>label</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom796</globalKey>
        <inputFieldName>developerName</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>apiName</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9270</globalKey>
        <inputFieldName>redirectType</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>redirectType</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom1757</globalKey>
        <inputFieldName>redirectLink</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>redirect</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9837</globalKey>
        <inputFieldName>feiId</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>feiId</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom6481</globalKey>
        <inputFieldName>type</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>type</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>TransformPreferitiStructureCustom9584</globalKey>
        <inputFieldName>MasterLabel</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>TransformPreferitiStructure</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>masterLabel</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>[ {
  &quot;params&quot; : null,
  &quot;redirectType&quot; : null,
  &quot;redirectLink&quot; : null,
  &quot;type&quot; : &quot;FEI&quot;,
  &quot;feiId&quot; : &quot;PREVENTIVI.AUTORIZZAZIONI&quot;,
  &quot;developerName&quot; : &quot;UN_PREVAUTORIZZ&quot;,
  &quot;masterLabel&quot; : &quot;Preventivi e Autorizzazioni&quot;
}, {
  &quot;type&quot; : &quot;FEI&quot;,
  &quot;params&quot; : &quot;{\&quot;parametri\&quot;:\&quot;RA  CBDA\&quot;,\&quot;itemCod\&quot;:\&quot;001001002000000000000000000000\&quot;,\&quot;itemId\&quot;:\&quot;0614000000\&quot;}&quot;,
  &quot;redirectLink&quot; : null,
  &quot;developerName&quot; : &quot;001001002000000000000000000000&quot;,
  &quot;masterLabel&quot; : &quot;Consultazione BDA&quot;
}, {
  &quot;params&quot; : &quot;{\&quot;agef\&quot;:null,\&quot;agem\&quot;:null,\&quot;userId\&quot;:null}&quot;,
  &quot;redirectType&quot; : null,
  &quot;redirectLink&quot; : null,
  &quot;type&quot; : &quot;FEI&quot;,
  &quot;feiId&quot; : &quot;PREVENTIVATORE.UNISALUTE&quot;,
  &quot;developerName&quot; : &quot;US_PREVUNISALUTE&quot;,
  &quot;masterLabel&quot; : &quot;Preventivatore UniSalute&quot;
} ]</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>TransformPreferitiStructure_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
