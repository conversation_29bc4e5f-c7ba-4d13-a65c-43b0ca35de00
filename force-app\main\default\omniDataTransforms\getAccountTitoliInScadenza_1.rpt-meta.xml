<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>getAccountTitoliInScadenza</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem14</globalKey>
        <inputFieldName>accAccRelation:FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:nomeSocieta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem17</globalKey>
        <inputFieldName>Account:Id</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:id</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem15</globalKey>
        <inputFieldName>accAccRelation:FinServ__Account__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>Account:codiceFiscale</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem16</globalKey>
        <inputFieldName>Account:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:codiceAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:permissionSetAssignmentUnipolSai LISTSIZE 0 &gt;</formulaConverted>
        <formulaExpression>LISTSIZE(permissionSetAssignmentUnipolSai)&gt;0</formulaExpression>
        <formulaResultPath>UnipolSai</formulaResultPath>
        <formulaSequence>2.0</formulaSequence>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem8</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom5381</globalKey>
        <inputFieldName>AccountSoc:FinServ__RelatedAccount__r.AgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;SOC_1&quot; : &quot;1&quot;,
  &quot;SOC_4&quot; : &quot;4&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom8713</globalKey>
        <inputFieldName>AccountSoc:FinServ__RelatedAccount__r.Name</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:nomeSocieta</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem20</globalKey>
        <inputFieldName>accAccRelation:FinServ__Account__r.ExternalId__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>CF</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem13</globalKey>
        <inputFieldName>accAccRelation:FinServ__RelatedAccount__r.AgencyCode__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>Account:societa</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <transformValuesMappings>{
  &quot;SOC_1&quot; : &quot;1&quot;,
  &quot;SOC_4&quot; : &quot;4&quot;
}</transformValuesMappings>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>idToSearch</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem10</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accAccRelation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;MandatoUniSalute&apos;</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem2</globalKey>
        <inputFieldName>PermissionSet.Name</inputFieldName>
        <inputObjectName>PermissionSetAssignment</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>permissionSetAssignmentUniSalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem3</globalKey>
        <inputFieldName>AssigneeId</inputFieldName>
        <inputObjectName>PermissionSetAssignment</inputObjectName>
        <inputObjectQuerySequence>3.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>permissionSetAssignmentUniSalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;MandatoUnipolSai&apos;</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem6</globalKey>
        <inputFieldName>PermissionSet.Name</inputFieldName>
        <inputObjectName>PermissionSetAssignment</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>permissionSetAssignmentUnipolSai</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem7</globalKey>
        <inputFieldName>AssigneeId</inputFieldName>
        <inputObjectName>PermissionSetAssignment</inputObjectName>
        <inputObjectQuerySequence>2.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>permissionSetAssignmentUnipolSai</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>UserId</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem11</globalKey>
        <inputFieldName>Id</inputFieldName>
        <inputObjectName>User</inputObjectName>
        <inputObjectQuerySequence>1.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>User</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem21</globalKey>
        <inputFieldName>UniSalute</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>permissionSetAssignmentUnisalute</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <defaultValue>false</defaultValue>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem18</globalKey>
        <inputFieldName>UnipolSai</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>Boolean</outputFieldFormat>
        <outputFieldName>permissionSetAssignmentUnipolSai</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom9049</globalKey>
        <inputFieldName>AccountSoc:Identifier__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>List&lt;Map&gt;</outputFieldFormat>
        <outputFieldName>Account:codiceAgenzia</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;AgencySociety&apos;</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom6871</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountSoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>User:IdAzienda__c</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom1507</globalKey>
        <inputFieldName>FinServ__Account__c</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>5.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>AccountSoc</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <filterOperator>=</filterOperator>
        <filterValue>&apos;AccountSociety&apos;</filterValue>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem5</globalKey>
        <inputFieldName>RecordType.DeveloperName</inputFieldName>
        <inputObjectName>FinServ__AccountAccountRelation__c</inputObjectName>
        <inputObjectQuerySequence>4.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>accAccRelation</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| var:permissionSetAssignmentUniSalute LISTSIZE 0 &gt;</formulaConverted>
        <formulaExpression>LISTSIZE(permissionSetAssignmentUniSalute)&gt;0</formulaExpression>
        <formulaResultPath>UniSalute</formulaResultPath>
        <formulaSequence>1.0</formulaSequence>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem4</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem19</globalKey>
        <inputFieldName>Account:SourceSocietyDescription__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldFormat>String</outputFieldFormat>
        <outputFieldName>accountSociety</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>true</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem12</globalKey>
        <inputFieldName>Account:VatNumber__c</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>PIVA</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <formulaConverted>| | var:accAccRelation:FinServ__Account__r.RecordType.DeveloperName &quot;PersonAccount&quot; = STRINGINDEXOF var:accAccRelation:FinServ__Account__r.FinServ__TaxId__pc var:accAccRelation:FinServ__Account__r.VatNumber__c IF</formulaConverted>
        <formulaExpression>IF(STRINGINDEXOF(accAccRelation:FinServ__Account__r.RecordType.DeveloperName = &quot;PersonAccount&quot;), accAccRelation:FinServ__Account__r.FinServ__TaxId__pc, accAccRelation:FinServ__Account__r.VatNumber__c)</formulaExpression>
        <formulaResultPath>CF1</formulaResultPath>
        <formulaSequence>3.0</formulaSequence>
        <globalKey>getAccountTitoliInScadenzaCustom0jI9O000000u08bUAAItem0</globalKey>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>getAccountTitoliInScadenza</name>
        <outputCreationSequence>0.0</outputCreationSequence>
        <outputFieldName>Formula</outputFieldName>
        <outputObjectName>Formula</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;UserId&quot; : &quot;0059O00000WEYgvQAH&quot;,
  &quot;idToSearch&quot; : &quot;0019O00000wcIddQAE&quot;
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Extract</type>
    <uniqueName>getAccountTitoliInScadenza_2</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
