<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <assignments>
        <name>Assign_Case_AccountId</name>
        <label>Assign Case.AccountId</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.AccountId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Contratti_Quadro.firstSelectedRow.AccountId</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_case_fields</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_entit_chiamante</name>
        <label>Assign case entità chiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.EntChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>Entita_chiamante</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Check_Entit_chiamante_selezionata</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_fields</name>
        <label>Assign case fields</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.OwnerId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>PicklistUfficio</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UfficioApertura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getNameSelectedGroup.Name</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UtAssegnatario__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Create_Case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_case_RT</name>
        <label>Assign case RT</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.RecordTypeId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getCaseRecordTypeInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.Origin</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Phone</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_Contratti_Quadro</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_FM_contact_info</name>
        <label>Assign FM contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getCurrentContactFMInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.NomeChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>NominativoFM</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.EmailChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>EmailFM.value</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.TelChiamante__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>TelefonoFM.value</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invioassign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_FM_contact_info_ar</name>
        <label>Assign FM contact info</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.ContactId</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>getCurrentContactFMInfo.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>assign_invioassign_invio_email_automatico</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>assign_invioassign_invio_email_automatico</name>
        <label>assign invioassign invio email automatico</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.InvioEmailAutomatico__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsg</name>
        <label>Assign ShowRequiredMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_showRequiredMsg_false</name>
        <label>Assign showRequiredMsg false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsg</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>false</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Risolto_in_chiamata_false</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Assign_ShowRequiredMsgNote</name>
        <label>Assign ShowRequiredMsgNote</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>ShowRequiredMsgNote</assignToReference>
            <operator>Assign</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </assignmentItems>
        <connector>
            <isGoTo>true</isGoTo>
            <targetReference>Screen_campi_case</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignStatusClosed</name>
        <label>AssignStatusClosed</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>Chiuso - Risolto</stringValue>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.ClosedDate</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$Flow.CurrentDateTime</elementReference>
            </value>
        </assignmentItems>
        <assignmentItems>
            <assignToReference>caseRecord.UtenteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_AccountId</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>AssignStatusInGestione</name>
        <label>AssignStatusInGestione</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.Status</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>In gestione</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>Assign_Case_AccountId</targetReference>
        </connector>
    </assignments>
    <assignments>
        <name>Clean_Note_chiusura</name>
        <label>Clean Note chiusura</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignmentItems>
            <assignToReference>caseRecord.NoteChiusura__c</assignToReference>
            <operator>Assign</operator>
            <value>
                <stringValue>null</stringValue>
            </value>
        </assignmentItems>
        <connector>
            <targetReference>CheckZonaCircRequired</targetReference>
        </connector>
    </assignments>
    <choices>
        <name>AltroRichiedenteChoice</name>
        <choiceText>Altro Richiedente</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Altro Richiedente</stringValue>
        </value>
    </choices>
    <choices>
        <name>FleetManagerChoice</name>
        <choiceText>Fleet Manager</choiceText>
        <dataType>String</dataType>
        <value>
            <stringValue>Fleet Manager</stringValue>
        </value>
    </choices>
    <customProperties>
        <name>ScreenProgressIndicator</name>
        <value>
            <stringValue>{&quot;location&quot;:&quot;top&quot;,&quot;type&quot;:&quot;simple&quot;}</stringValue>
        </value>
    </customProperties>
    <decisions>
        <name>assegnato_a_uffici_rental</name>
        <label>è assegnato a uffici rental?</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsgUfficio</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Outcome_1_of_Decision_6</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>ufficiQueueList</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getRecordTypeFM</targetReference>
            </connector>
            <label>true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_contratti_1</name>
        <label>Check contratti &gt;=1</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>ScreenErrorMsg</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>contratti_1</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getContrattiQuadro</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getCaseRecordTypeInfo</targetReference>
            </connector>
            <label>contratti &gt;=1</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_current_Record</name>
        <label>Check current record</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default</defaultConnectorLabel>
        <rules>
            <name>FM</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>getCurrentContactFMInfo</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>getRecordTypeSCQuadro</targetReference>
            </connector>
            <label>FM</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Entit_chiamante_selezionata</name>
        <label>Check Entità chiamante selezionata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>assign_invioassign_invio_email_automatico</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Fleet_Manager</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Fleet Manager</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiFM</targetReference>
            </connector>
            <label>Fleet Manager</label>
        </rules>
        <rules>
            <name>Altro_Richiedente</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.EntChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Altro Richiedente</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>ScreenDatiAltroRichiedente</targetReference>
            </connector>
            <label>Altro Richiedente</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_flag_result</name>
        <label>Check flag result</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Screen_Ufficio</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_true</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>isZonaCircrRequired</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Zona_di_circolazione__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <isGoTo>true</isGoTo>
                <targetReference>Screen_campi_case</targetReference>
            </connector>
            <label>is true</label>
        </rules>
    </decisions>
    <decisions>
        <name>Checkcampirequired</name>
        <label>Check campi required</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>Assign_showRequiredMsg_false</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>esistecamponull</name>
            <conditionLogic>or</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Origin</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.TipoRichiesta__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Categoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.SottoCategoria__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Subject</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.Description</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsg</targetReference>
            </connector>
            <label>esiste campo null</label>
        </rules>
        <rules>
            <name>RisoltoInChiamata_true_and_note_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.NoteChiusura__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Assign_ShowRequiredMsgNote</targetReference>
            </connector>
            <label>RisoltoInChiamata true and note null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Risolto_in_Chiamata</name>
        <label>Risolto in Chiamata</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>AssignStatusInGestione</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>isTrue</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>AssignStatusClosed</targetReference>
            </connector>
            <label>isTrue</label>
        </rules>
    </decisions>
    <decisions>
        <name>Risolto_in_chiamata_false</name>
        <label>Risolto in chiamata false</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>CheckZonaCircRequired</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>is_false</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>caseRecord.NoteChiusura__c</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Clean_Note_chiusura</targetReference>
            </connector>
            <label>is false</label>
        </rules>
    </decisions>
    <description>Flusso di creazione case da/per conto di un FM su contratto quadro</description>
    <dynamicChoiceSets>
        <name>QueuesCollection</name>
        <collectionReference>ufficiQueueList</collectionReference>
        <dataType>String</dataType>
        <displayField>Name</displayField>
        <object>CollaborationGroup</object>
        <valueField>Id</valueField>
    </dynamicChoiceSets>
    <environments>Default</environments>
    <formulas>
        <name>currentUserName</name>
        <dataType>String</dataType>
        <expression>{!$User.FirstName} +&apos; &apos;+ {!$User.LastName}</expression>
    </formulas>
    <interviewLabel>urcs_CaseCRMFleetManager {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_CaseCRMFleetManager</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>Flow</processType>
    <recordCreates>
        <name>Create_Case</name>
        <label>Create Case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <faultConnector>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <inputReference>caseRecord</inputReference>
    </recordCreates>
    <recordLookups>
        <name>getCaseRecordTypeInfo</name>
        <label>getCaseRecordTypeInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Assign_case_RT</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Case</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_CaseCRM</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getContrattiQuadro</name>
        <label>getContrattiQuadro</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_contratti_1</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>AccountId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getCurrentContactFMInfo.AccountId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getRecordTypeSCQuadro.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>ServiceContract</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getCurrentContactFMInfo</name>
        <label>getCurrentContactFMInfo</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_current_Record</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>recordId</elementReference>
            </value>
        </filters>
        <filters>
            <field>RecordTypeId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>getRecordTypeFM.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Contact</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>GetCurrentUserGroupMembership</name>
        <label>GetCurrentUserGroupMembership</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>assegnato_a_uffici_rental</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>UserOrGroupId</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>$User.Id</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>false</getFirstRecordOnly>
        <object>GroupMember</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getNameSelectedGroup</name>
        <label>getNameSelectedGroup</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Risolto_in_Chiamata</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Id</field>
            <operator>EqualTo</operator>
            <value>
                <elementReference>PicklistUfficio</elementReference>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>Group</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getRecordTypeFM</name>
        <label>getRecordTypeFM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getCurrentContactFMInfo</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Contact</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_FleetManager</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>getRecordTypeSCQuadro</name>
        <label>getRecordTypeSCQuadro</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>getContrattiQuadro</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>ErrorOnInsert</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>SobjectType</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ServiceContract</stringValue>
            </value>
        </filters>
        <filters>
            <field>DeveloperName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>ur_Quadro</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>RecordType</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <screens>
        <name>ErrorOnInsert</name>
        <label>ErrorOnInsert</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsgonInsert</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;Si è verificato un errore: impossibile completare l&apos;operazione.&lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;br&gt;&lt;/p&gt;&lt;p&gt;&lt;span style=&quot;font-size: inherit; color: rgb(255, 0, 0);&quot;&gt;﻿{!$Flow.FaultMessage}&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>Screen_campi_case</name>
        <label>Screen campi case</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Checkcampirequired</targetReference>
        </connector>
        <fields>
            <name>RequiredMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;I seguenti campi sono obbligatori: &lt;/span&gt;&lt;/p&gt;&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;Origine, Tipologia Richiesta, Categoria, Sottocategoria, Oggetto, Descrizione.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsg</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>requiredMsgZonacirc</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Il campo Zona di Circolazione è obbligatorio per questa combinazione di categoria e sottocategoria.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>isZonaCircrRequired</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>RequiredMsgNote</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255); color: rgb(255, 0, 0);&quot;&gt;Il campo Note chiusura è obbligatorio in caso di risoluzione in chiamata.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgNote</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>Screen_campi_case_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Screen_campi_case_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Origin</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TipoRichiesta__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Categoria__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.SottoCategoria__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>Screen_campi_case_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Zona_di_circolazione__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Subject</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Description</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.Risolto__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.NoteChiusura__c</objectFieldReference>
                    <visibilityRule>
                        <conditionLogic>and</conditionLogic>
                        <conditions>
                            <leftValueReference>caseRecord.Risolto__c</leftValueReference>
                            <operator>EqualTo</operator>
                            <rightValue>
                                <booleanValue>true</booleanValue>
                            </rightValue>
                        </conditions>
                    </visibilityRule>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.InvioEmailAutomatico__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_2</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>Screen_Contratti_Quadro</name>
        <label>Screen Contratti Quadro</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>ScreenEntitaChiamante</targetReference>
        </connector>
        <fields>
            <name>Contratti_Quadro</name>
            <dataTypeMappings>
                <typeName>T</typeName>
                <typeValue>ServiceContract</typeValue>
            </dataTypeMappings>
            <extensionName>flowruntime:datatable</extensionName>
            <fieldType>ComponentInstance</fieldType>
            <inputParameters>
                <name>selectionMode</name>
                <value>
                    <stringValue>SINGLE_SELECT</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>minRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>tableData</name>
                <value>
                    <elementReference>getContrattiQuadro</elementReference>
                </value>
            </inputParameters>
            <inputParameters>
                <name>isShowSearchBar</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>shouldDisplayLabel</name>
                <value>
                    <booleanValue>true</booleanValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>maxRowSelection</name>
                <value>
                    <numberValue>1.0</numberValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>columns</name>
                <value>
                    <stringValue>[{&quot;apiName&quot;:&quot;Name&quot;,&quot;guid&quot;:&quot;column-6e7d&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:0,&quot;label&quot;:&quot;Contract Name&quot;,&quot;type&quot;:&quot;text&quot;},{&quot;apiName&quot;:&quot;AccountName__c&quot;,&quot;guid&quot;:&quot;column-8e1c&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:1,&quot;label&quot;:&quot;Cliente Contratto&quot;,&quot;type&quot;:&quot;customRichText&quot;},{&quot;apiName&quot;:&quot;Stato__c&quot;,&quot;guid&quot;:&quot;column-e518&quot;,&quot;editable&quot;:false,&quot;hasCustomHeaderLabel&quot;:false,&quot;customHeaderLabel&quot;:&quot;&quot;,&quot;wrapText&quot;:true,&quot;order&quot;:2,&quot;label&quot;:&quot;Stato&quot;,&quot;type&quot;:&quot;text&quot;}]</stringValue>
                </value>
            </inputParameters>
            <inputParameters>
                <name>label</name>
                <value>
                    <stringValue>Contratti Quadro</stringValue>
                </value>
            </inputParameters>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
            <storeOutputAutomatically>true</storeOutputAutomatically>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_1</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>Screen_Ufficio</name>
        <label>Screen Ufficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>getNameSelectedGroup</targetReference>
        </connector>
        <fields>
            <name>PicklistUfficio</name>
            <choiceReferences>QueuesCollection</choiceReferences>
            <dataType>String</dataType>
            <fieldText>Ufficio Apertura:</fieldText>
            <fieldType>DropdownBox</fieldType>
            <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
            <isRequired>true</isRequired>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenDatiAltroRichiedente</name>
        <label>ScreenDatiAltroRichiedente</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_FM_contact_info_ar</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiAltroRichiedente_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiAltroRichiedente_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_2_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiAltroRichiedente_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_2_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto del Fleet Manager&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_2_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getCurrentContactFMInfo.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Dati_Altro_Richiedente</name>
            <fieldText>Dati Altro Richiedente</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Altro_Richiedente_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.NomeChiamante__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.EmailChiamante__c</objectFieldReference>
                </fields>
                <fields>
                    <fieldType>ObjectProvided</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>false</isRequired>
                    <objectFieldReference>caseRecord.TelChiamante__c</objectFieldReference>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenDatiFM</name>
        <label>ScreenDatiFM</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_FM_contact_info</targetReference>
        </connector>
        <fields>
            <name>ScreenDatiFM_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenDatiFM_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_EntChiamSelLabel</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;Entità chiamante selezionata&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_1_of_EntChiamSel</name>
                    <fieldText>&lt;p&gt;{!caseRecord.EntChiamante__c}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenDatiFM_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Copy_1_of_Copy_1_of_LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt; per conto del Fleet Manager&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>Copy_1_of_Copy_1_of_driverName</name>
                    <fieldText>&lt;p&gt;&lt;span style=&quot;background-color: rgb(255, 255, 255);&quot;&gt;{!getCurrentContactFMInfo.Name}&lt;/span&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <fields>
            <name>Dati_Fleet_Manager</name>
            <fieldText>Dati Chiamante</fieldText>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>Dati_Fleet_Manager_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>NominativoFM</name>
                    <dataType>String</dataType>
                    <defaultValue>
                        <elementReference>getCurrentContactFMInfo.Name</elementReference>
                    </defaultValue>
                    <fieldText>Riferimento entità chiamante</fieldText>
                    <fieldType>InputField</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isReadOnly>
                        <booleanValue>true</booleanValue>
                    </isReadOnly>
                    <isRequired>false</isRequired>
                </fields>
                <fields>
                    <name>EmailFM</name>
                    <extensionName>flowruntime:email</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Email Entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getCurrentContactFMInfo.Email</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <fields>
                    <name>TelefonoFM</name>
                    <extensionName>flowruntime:phone</extensionName>
                    <fieldType>ComponentInstance</fieldType>
                    <inputParameters>
                        <name>label</name>
                        <value>
                            <stringValue>Telefono entità chiamante</stringValue>
                        </value>
                    </inputParameters>
                    <inputParameters>
                        <name>value</name>
                        <value>
                            <elementReference>getCurrentContactFMInfo.Phone</elementReference>
                        </value>
                    </inputParameters>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                    <storeOutputAutomatically>true</storeOutputAutomatically>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>12</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>step4</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenEntitaChiamante</name>
        <label>ScreenEntitaChiamante</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <connector>
            <targetReference>Assign_case_entit_chiamante</targetReference>
        </connector>
        <fields>
            <name>ShowRequiredMsgECText</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;&lt;span style=&quot;color: rgb(255, 0, 0);&quot;&gt;E&apos; obbligatorio selezionare l&apos;entità chiamante.&lt;/span&gt;&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
            <visibilityRule>
                <conditionLogic>and</conditionLogic>
                <conditions>
                    <leftValueReference>ShowRequiredMsgEC</leftValueReference>
                    <operator>EqualTo</operator>
                    <rightValue>
                        <booleanValue>true</booleanValue>
                    </rightValue>
                </conditions>
            </visibilityRule>
        </fields>
        <fields>
            <name>ScreenEntitaChiamante_Section1</name>
            <fieldType>RegionContainer</fieldType>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column1</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>Entita_chiamante</name>
                    <choiceReferences>FleetManagerChoice</choiceReferences>
                    <choiceReferences>AltroRichiedenteChoice</choiceReferences>
                    <dataType>String</dataType>
                    <fieldText>Entità chiamante</fieldText>
                    <fieldType>DropdownBox</fieldType>
                    <inputsOnNextNavToAssocScrn>UseStoredValues</inputsOnNextNavToAssocScrn>
                    <isRequired>true</isRequired>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <fields>
                <name>ScreenEntitaChiamante_Section1_Column2</name>
                <fieldType>Region</fieldType>
                <fields>
                    <name>LabelDriver</name>
                    <fieldText>&lt;p&gt;&lt;strong&gt;per conto del Fleet Manager&lt;/strong&gt;&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <fields>
                    <name>driverName</name>
                    <fieldText>&lt;p&gt;{!getCurrentContactFMInfo.Name}&lt;/p&gt;</fieldText>
                    <fieldType>DisplayText</fieldType>
                </fields>
                <inputParameters>
                    <name>width</name>
                    <value>
                        <stringValue>6</stringValue>
                    </value>
                </inputParameters>
                <isRequired>false</isRequired>
            </fields>
            <isRequired>false</isRequired>
            <regionContainerType>SectionWithoutHeader</regionContainerType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
        <stageReference>
            <elementReference>Step_3</elementReference>
        </stageReference>
    </screens>
    <screens>
        <name>ScreenErrorMsg</name>
        <label>ScreenErrorMsg</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>false</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>false</allowPause>
        <fields>
            <name>ErrorMsg</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;Non sono disponibili contratti da selezionare.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <screens>
        <name>ScreenErrorMsgUfficio</name>
        <label>ScreenErrorMsgUfficio</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <allowBack>true</allowBack>
        <allowFinish>true</allowFinish>
        <allowPause>true</allowPause>
        <fields>
            <name>ErrMsgUfficio</name>
            <fieldText>&lt;p style=&quot;text-align: center;&quot;&gt;L&apos;utente corrente non è assegnato a nessun ufficio.&lt;/p&gt;</fieldText>
            <fieldType>DisplayText</fieldType>
        </fields>
        <showFooter>true</showFooter>
        <showHeader>true</showHeader>
    </screens>
    <stages>
        <name>step4</name>
        <isActive>false</isActive>
        <label>step4</label>
        <stageOrder>4</stageOrder>
    </stages>
    <stages>
        <name>Step_1</name>
        <isActive>true</isActive>
        <label>Step_1</label>
        <stageOrder>1</stageOrder>
    </stages>
    <stages>
        <name>Step_2</name>
        <isActive>false</isActive>
        <label>Step_2</label>
        <stageOrder>2</stageOrder>
    </stages>
    <stages>
        <name>Step_3</name>
        <isActive>false</isActive>
        <label>Step_3</label>
        <stageOrder>3</stageOrder>
    </stages>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>urcs_CaseCheckUfficiOperatore</targetReference>
        </connector>
    </start>
    <status>Active</status>
    <subflows>
        <name>CheckZonaCircRequired</name>
        <label>CheckZonaCircRequired</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Check_flag_result</targetReference>
        </connector>
        <flowName>urcs_CaseCheckZonaCircRequired</flowName>
        <inputAssignments>
            <name>Categoria</name>
            <value>
                <elementReference>caseRecord.Categoria__c</elementReference>
            </value>
        </inputAssignments>
        <inputAssignments>
            <name>Sottocategoria</name>
            <value>
                <elementReference>caseRecord.SottoCategoria__c</elementReference>
            </value>
        </inputAssignments>
        <outputAssignments>
            <assignToReference>isZonaCircrRequired</assignToReference>
            <name>isZonaCircrRequired</name>
        </outputAssignments>
    </subflows>
    <subflows>
        <name>urcs_CaseCheckUfficiOperatore</name>
        <label>urcs_CaseCheckUfficiOperatore</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>GetCurrentUserGroupMembership</targetReference>
        </connector>
        <flowName>urcs_CaseCheckUfficiOperatore</flowName>
        <outputAssignments>
            <assignToReference>ufficiQueueList</assignToReference>
            <name>ufficiQueueList</name>
        </outputAssignments>
    </subflows>
    <variables>
        <name>caseRecord</name>
        <dataType>SObject</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>true</isOutput>
        <objectType>Case</objectType>
    </variables>
    <variables>
        <name>contractIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>groupIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>isZonaCircrRequired</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>quadrocContractIdList</name>
        <dataType>String</dataType>
        <isCollection>true</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>recordId</name>
        <dataType>String</dataType>
        <isCollection>false</isCollection>
        <isInput>true</isInput>
        <isOutput>true</isOutput>
    </variables>
    <variables>
        <name>ShowRequiredMsg</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ShowRequiredMsgEC</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
    </variables>
    <variables>
        <name>ShowRequiredMsgNote</name>
        <dataType>Boolean</dataType>
        <isCollection>false</isCollection>
        <isInput>false</isInput>
        <isOutput>false</isOutput>
        <value>
            <booleanValue>false</booleanValue>
        </value>
    </variables>
    <variables>
        <name>ufficiQueueList</name>
        <dataType>SObject</dataType>
        <isCollection>true</isCollection>
        <isInput>true</isInput>
        <isOutput>false</isOutput>
        <objectType>CollaborationGroup</objectType>
    </variables>
</Flow>
