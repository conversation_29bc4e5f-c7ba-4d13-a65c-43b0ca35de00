<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Unipolsai</authorName>
    <clonedFromOmniUiCardKey>UniProdottiAssicurativi/Unipolsai/6.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_ProdottiAssicurativi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;,&quot;showOtherAgency&quot;:&quot;{Session.showOtherAgency}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;showOtherAgency\&quot;:\&quot;{showOtherAgency}\&quot;,\&quot;Session.showOtherAgency\&quot;:\&quot;{Session.showOtherAgency}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X00000tIvmXQAS&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;{Session.showOtherAgency}&quot;,&quot;id&quot;:53},{&quot;name&quot;:&quot;Session.showOtherAgency&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:12}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <name>UniProdottiAssicurativiHeader</name>
    <omniUiCardType>Child</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[{&quot;id&quot;:&quot;state-new-condition-4&quot;,&quot;field&quot;:&quot;Session.showOtherAgency&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;false&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false},{&quot;id&quot;:&quot;state-new-condition-12&quot;,&quot;field&quot;:&quot;Session.showOtherAgency&quot;,&quot;operator&quot;:&quot;==&quot;,&quot;value&quot;:&quot;&quot;,&quot;type&quot;:&quot;custom&quot;,&quot;hasMergeField&quot;:false,&quot;logicalOperator&quot;:&quot;||&quot;}]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;SameAgency&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;class&quot;:&quot;slds-card &quot;,&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Block&quot;,&quot;element&quot;:&quot;block&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Block&quot;,&quot;collapsible&quot;:false,&quot;record&quot;:&quot;{record}&quot;,&quot;collapsedByDefault&quot;:false,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;block&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;},{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-p-top_x-small slds-p-bottom_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;5px 5px 0 0&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:5px 5px 0 0;     &quot;},&quot;children&quot;:[{&quot;name&quot;:&quot;Icon&quot;,&quot;element&quot;:&quot;flexIcon&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;iconType&quot;:&quot;Salesforce SVG&quot;,&quot;iconName&quot;:&quot;standard:opportunity&quot;,&quot;size&quot;:&quot;medium&quot;,&quot;extraclass&quot;:&quot;slds-icon_container slds-icon-standard-opportunity&quot;,&quot;variant&quot;:&quot;inverse&quot;,&quot;imgsrc&quot;:&quot;&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;CustomIcon&quot;,&quot;class&quot;:&quot;slds-p-left_x-small CustomIcon&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Icon-0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_1-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;1&quot;},&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;customClass&quot;:&quot;CustomIcon&quot;,&quot;class&quot;:&quot;slds-p-left_x-small CustomIcon&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_flexIcon_0_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_left%22%3E%0A%3Cdiv%20class=%22slds-text-align_left%22%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cstrong%20class=%22slds-text-heading_medium%22%3EProdotti%20Assicurativi%20(%7BprodottiAssicurativi.same_agency.total_count%7D)%3C/strong%3E%3C/span%3E%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-p-left_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;5&quot;},&quot;sizeClass&quot;:&quot;slds-size_5-of-12 &quot;,&quot;padding&quot;:[{&quot;type&quot;:&quot;left&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;left:x-small&quot;}],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-p-left_x-small &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}],&quot;key&quot;:&quot;element_element_block_0_0_outputField_1_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;},{&quot;name&quot;:&quot;Text&quot;,&quot;element&quot;:&quot;outputField&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;mergeField&quot;:&quot;%3Cdiv%20class=%22slds-text-align_right%22%3E%0A%3Cdiv%20class=%22slds-text-align_right%22%3E%3Cstrong%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%3Cspan%20style=%22font-size:%2010pt;%20color:%20#444444;%22%3EPremio%20Totale%20%3C/span%3E%3C/span%3E%3Cspan%20style=%22font-size:%2014pt;%22%3E%7BprodottiAssicurativi.same_agency.total_premium_amount%7D%3C/span%3E%3C/strong%3E%3C/div%3E%0A%3C/div%3E&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;data-conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]}},&quot;type&quot;:&quot;text&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;elementLabel&quot;:&quot;Block-1-Text-2&quot;,&quot;key&quot;:&quot;element_element_block_0_0_outputField_2_0&quot;,&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;&quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]},{&quot;key&quot;:&quot;element_element_block_0_0_action_3_0&quot;,&quot;name&quot;:&quot;Action&quot;,&quot;element&quot;:&quot;action&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;label&quot;:&quot;Nuovo Prodotto&quot;,&quot;iconName&quot;:&quot;standard-default&quot;,&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;stateObj&quot;:&quot;{record}&quot;,&quot;actionList&quot;:[{&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752070007900&quot;,&quot;type&quot;:&quot;Flyout&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;flyoutType&quot;:&quot;OmniScripts&quot;,&quot;openFlyoutIn&quot;:&quot;Modal&quot;,&quot;channelName&quot;:&quot;&quot;,&quot;layoutType&quot;:&quot;lightning&quot;,&quot;osName&quot;:&quot;UniProdotto/Nuovo/Italian&quot;,&quot;flyoutLwc&quot;:&quot;uni-prodotto-nuovo-italian&quot;,&quot;hasExtraParams&quot;:true,&quot;flyoutParams&quot;:{&quot;ContextId&quot;:&quot;{recordId}&quot;}},&quot;key&quot;:&quot;1752069830311-gnu6yvdqk&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:true,&quot;isOpen&quot;:true,&quot;actionIndex&quot;:0,&quot;isTrackingDisabled&quot;:true}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;hideActionIcon&quot;:true,&quot;displayAsButton&quot;:true,&quot;buttonVariant&quot;:&quot;neutral&quot;,&quot;flyoutChannel&quot;:&quot;&quot;,&quot;flyoutDetails&quot;:{&quot;openFlyoutIn&quot;:&quot;Modal&quot;}},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;parentElementKey&quot;:&quot;element_block_0_0&quot;,&quot;elementLabel&quot;:&quot;Block-1-Action-3&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_3-of-12 &quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;3&quot;},&quot;padding&quot;:[],&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:&quot;&quot;,&quot;width&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;,&quot;radius&quot;:&quot;&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;center&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;class&quot;:&quot;slds-text-align_center &quot;,&quot;style&quot;:&quot;      \n         &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}],&quot;elementLabel&quot;:&quot;Block-1&quot;,&quot;styleObjects&quot;:[{&quot;key&quot;:0,&quot;conditions&quot;:&quot;default&quot;,&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;top&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;top:x-small&quot;},{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;,&quot;label&quot;:&quot;bottom:x-small&quot;}],&quot;class&quot;:&quot;slds-border_top slds-border_right slds-border_left slds-p-top_x-small slds-p-bottom_x-small &quot;,&quot;sizeClass&quot;:&quot;slds-size_12-of-12 &quot;,&quot;margin&quot;:[],&quot;background&quot;:{&quot;color&quot;:&quot;#f3f3f3&quot;,&quot;image&quot;:&quot;&quot;,&quot;size&quot;:&quot;&quot;,&quot;repeat&quot;:&quot;&quot;,&quot;position&quot;:&quot;&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;container&quot;:{&quot;class&quot;:&quot;&quot;},&quot;border&quot;:{&quot;type&quot;:[&quot;border_top&quot;,&quot;border_right&quot;,&quot;border_left&quot;],&quot;width&quot;:&quot;1&quot;,&quot;color&quot;:&quot;#E5E5E5&quot;,&quot;radius&quot;:&quot;5px 5px 0 0&quot;,&quot;style&quot;:&quot;&quot;},&quot;elementStyleProperties&quot;:{},&quot;text&quot;:{&quot;align&quot;:&quot;&quot;,&quot;color&quot;:&quot;&quot;},&quot;inlineStyle&quot;:&quot;&quot;,&quot;style&quot;:&quot;background-color:#f3f3f3;     border-top: #E5E5E5 1px solid;border-right: #E5E5E5 1px solid;border-left: #E5E5E5 1px solid; \n    border-radius:5px 5px 0 0;     &quot;},&quot;label&quot;:&quot;Default&quot;,&quot;name&quot;:&quot;Default&quot;,&quot;conditionString&quot;:&quot;&quot;,&quot;draggable&quot;:false}]}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;ipMethod&quot;:&quot;UniDS_ProdottiAssicurativi&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;AccountId&quot;:&quot;{recordId}&quot;,&quot;UserId&quot;:&quot;{userId}&quot;,&quot;showOtherAgency&quot;:&quot;{Session.showOtherAgency}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;,\&quot;userId\&quot;:\&quot;{userId}\&quot;,\&quot;showOtherAgency\&quot;:\&quot;{showOtherAgency}\&quot;,\&quot;Session.showOtherAgency\&quot;:\&quot;{Session.showOtherAgency}\&quot;}&quot;,&quot;resultVar&quot;:&quot;&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;0019X00000tIvmXQAS&quot;,&quot;id&quot;:1},{&quot;name&quot;:&quot;userId&quot;,&quot;val&quot;:&quot;0059X00000Jr08cQAB&quot;,&quot;id&quot;:3},{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;{Session.showOtherAgency}&quot;,&quot;id&quot;:53},{&quot;name&quot;:&quot;Session.showOtherAgency&quot;,&quot;val&quot;:&quot;true&quot;,&quot;id&quot;:12}]},&quot;title&quot;:&quot;UniProdottiAssicurativiHeader&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;targetConfigs&quot;:&quot;CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ0NvbW11bml0eV9fRGVmYXVsdCI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJyZWNvcmRJZCIgdHlwZT0iU3RyaW5nIi8+CiAgICA8L3RhcmdldENvbmZpZz4KICA=&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;,&quot;lightning__RecordPage&quot;,&quot;lightningCommunity__Page&quot;,&quot;lightningCommunity__Default&quot;]},&quot;isExplicitImport&quot;:false,&quot;masterLabel&quot;:&quot;UniProdottiAssicurativiHeader&quot;},&quot;lwc&quot;:{&quot;DeveloperName&quot;:&quot;cfSS_Prodotti_Assicurativi_14_Unipolsai&quot;,&quot;Id&quot;:&quot;0Rb9O000003Kz2PSAS&quot;,&quot;MasterLabel&quot;:&quot;cfSS_Prodotti_Assicurativi_14_Unipolsai&quot;,&quot;NamespacePrefix&quot;:&quot;c&quot;,&quot;ManageableState&quot;:&quot;unmanaged&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightningCommunity__Default&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]}],&quot;isRepeatable&quot;:true,&quot;dynamicCanvasWidth&quot;:{&quot;type&quot;:&quot;desktop&quot;},&quot;globalCSS&quot;:true,&quot;sessionVars&quot;:[{&quot;name&quot;:&quot;showOtherAgency&quot;,&quot;val&quot;:&quot;&quot;,&quot;id&quot;:0}],&quot;events&quot;:[{&quot;eventname&quot;:&quot;ProdottiAgenciaEvent&quot;,&quot;channelname&quot;:&quot;ProdottiAgencia&quot;,&quot;element&quot;:&quot;action&quot;,&quot;eventtype&quot;:&quot;pubsub&quot;,&quot;recordIndex&quot;:&quot;0&quot;,&quot;actionList&quot;:[{&quot;key&quot;:&quot;1751966978775-m26g9c77a&quot;,&quot;label&quot;:&quot;Action&quot;,&quot;draggable&quot;:false,&quot;isOpen&quot;:true,&quot;card&quot;:&quot;{card}&quot;,&quot;stateAction&quot;:{&quot;id&quot;:&quot;flex-action-1752125692591&quot;,&quot;type&quot;:&quot;cardAction&quot;,&quot;displayName&quot;:&quot;Action&quot;,&quot;vlocityIcon&quot;:&quot;standard-default&quot;,&quot;targetType&quot;:&quot;Web Page&quot;,&quot;openUrlIn&quot;:&quot;Current Window&quot;,&quot;Web Page&quot;:{&quot;targetName&quot;:&quot;/apex&quot;},&quot;eventName&quot;:&quot;setValues&quot;,&quot;fieldValues&quot;:[{&quot;fieldName&quot;:&quot;Session.showOtherAgency&quot;,&quot;fieldValue&quot;:&quot;{action.showOtherAgency}&quot;},{&quot;fieldName&quot;:&quot;otherAgencyCount&quot;,&quot;fieldValue&quot;:&quot;{action.otherAgencyCount}&quot;},{&quot;fieldName&quot;:&quot;sameAgencyCount&quot;,&quot;fieldValue&quot;:&quot;{action.sameAgencyCount}&quot;}]},&quot;actionIndex&quot;:0}],&quot;showSpinner&quot;:&quot;false&quot;,&quot;key&quot;:&quot;event-0&quot;,&quot;displayLabel&quot;:&quot;ProdottiAgencia:ProdottiAgenciaEvent&quot;,&quot;eventLabel&quot;:&quot;pubsub&quot;}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;checkCp&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;allPermission&quot;:true,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;anyPermission&quot;:true,&quot;customerPermissionCheck&quot;:{&quot;X182_202030000&quot;:true,&quot;X182_202200200&quot;:true,&quot;X172_201030300&quot;:true,&quot;X172_201030700&quot;:true,&quot;X172_201030500&quot;:true,&quot;X172_201030900&quot;:true,&quot;X172_202150200&quot;:true,&quot;X172_202020102&quot;:true,&quot;X172_202020103&quot;:true,&quot;X172_202150000&quot;:true,&quot;X172_202020100&quot;:true,&quot;X172_202020101&quot;:true,&quot;X182_202010000&quot;:true,&quot;X182_206110100&quot;:true,&quot;X182_202210100&quot;:true,&quot;X182_201020000&quot;:true,&quot;X174_202000000&quot;:true,&quot;X172_202150600&quot;:true,&quot;X172_202020502&quot;:true,&quot;X172_202020503&quot;:true,&quot;X172_202150400&quot;:true,&quot;X172_202020500&quot;:true,&quot;X172_202020104&quot;:true,&quot;X172_202020501&quot;:true,&quot;X172_202020105&quot;:true,&quot;X172_202020504&quot;:true,&quot;X182_201010000&quot;:true,&quot;X182_202200100&quot;:true,&quot;X182_202120000&quot;:true,&quot;X172_201030400&quot;:true,&quot;X172_201030200&quot;:true,&quot;X172_201030800&quot;:true,&quot;X172_201030600&quot;:true,&quot;X172_202020000&quot;:true,&quot;X172_202150101&quot;:true,&quot;X172_202150100&quot;:true,&quot;X172_201031000&quot;:true,&quot;X174_201000000&quot;:true,&quot;X169_506000000&quot;:true,&quot;X182_202140000&quot;:true,&quot;X182_202210200&quot;:true,&quot;X182_206110200&quot;:true,&quot;X172_202150501&quot;:true,&quot;X172_202150303&quot;:true,&quot;X172_202150105&quot;:true,&quot;X172_202150500&quot;:true,&quot;X172_202150302&quot;:true,&quot;X172_202150104&quot;:true,&quot;X172_202150301&quot;:true,&quot;X172_202150103&quot;:true,&quot;X172_202150300&quot;:true,&quot;X172_202150102&quot;:true,&quot;X172_202020600&quot;:true,&quot;X172_202150504&quot;:true,&quot;X172_202150503&quot;:true,&quot;X172_202150305&quot;:true,&quot;X172_202150107&quot;:true,&quot;X172_202150502&quot;:true,&quot;X172_202150304&quot;:true,&quot;X172_202150106&quot;:true}},&quot;prodottiAssicurativi&quot;:{&quot;has_unica_policies&quot;:true,&quot;isAbbinato&quot;:false,&quot;other_agency&quot;:{&quot;salute&quot;:{&quot;count&quot;:0},&quot;casa_famiglia&quot;:{&quot;count&quot;:45},&quot;vita&quot;:{&quot;count&quot;:0},&quot;total_count&quot;:78,&quot;persona&quot;:{&quot;count&quot;:1},&quot;motor&quot;:{&quot;count&quot;:32}}}}</sampleDataSourceResponse>
    <stylingConfiguration>{&quot;customStyles&quot;:&quot;.CustomIcon{\r\n    width: 32px;\r\n    height: 32px;\r\n    margin-right: 8px;\r\n}&quot;}</stylingConfiguration>
    <versionNumber>2</versionNumber>
</OmniUiCard>
