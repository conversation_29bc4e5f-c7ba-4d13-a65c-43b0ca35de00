<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>Accept</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Accept</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ChangeStatus</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ChangeStatus</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ChangeStatus</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloneAsChild</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloneAsChild</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloneAsChild</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloseCase</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloseCase</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CloseCase</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MassClose</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MassClose</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>MassClose</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewCase</actionName>
        <content>newCaseORAura</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>LightningComponent</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewCase</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewCase</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>true</enableFeeds>
    <enableHistory>true</enableHistory>
    <externalSharingModel>Private</externalSharingModel>
    <recordTypeTrackFeedHistory>false</recordTypeTrackFeedHistory>
    <recordTypeTrackHistory>true</recordTypeTrackHistory>
    <searchLayouts>
        <customTabListAdditionalFields>CASES.CASE_NUMBER</customTabListAdditionalFields>
        <customTabListAdditionalFields>CASES.SUBJECT</customTabListAdditionalFields>
        <customTabListAdditionalFields>CASES.CREATED_DATE</customTabListAdditionalFields>
        <customTabListAdditionalFields>CASES.PRIORITY</customTabListAdditionalFields>
        <excludedStandardButtons>OpenListInQuip</excludedStandardButtons>
        <excludedStandardButtons>NewFromDocument</excludedStandardButtons>
        <excludedStandardButtons>MassAccept</excludedStandardButtons>
        <excludedStandardButtons>Accept</excludedStandardButtons>
        <excludedStandardButtons>MassClose</excludedStandardButtons>
        <excludedStandardButtons>ChangeOwner</excludedStandardButtons>
        <excludedStandardButtons>ChangeStatus</excludedStandardButtons>
        <excludedStandardButtons>MassChangeOwner</excludedStandardButtons>
        <excludedStandardButtons>MassMergeCase</excludedStandardButtons>
        <excludedStandardButtons>PrintableListView</excludedStandardButtons>
        <excludedStandardButtons>MassAddToActionableList</excludedStandardButtons>
        <excludedStandardButtons>MassAssignRecordLabel</excludedStandardButtons>
        <listViewButtons>StoricoCaricamenti</listViewButtons>
        <lookupDialogsAdditionalFields>CASES.CASE_NUMBER</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CASES.SUBJECT</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ACCOUNT.NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CASES.STATUS</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CASES.CASE_NUMBER</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CASES.SUBJECT</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ACCOUNT.NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CASES.STATUS</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>CASES.CASE_NUMBER</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CASES.SUBJECT</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CASES.STATUS</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CASES.CREATED_DATE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.ALIAS</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
