global with sharing class UniScraicaDocumentoController implements System.Callable {

    // Entry point for OmniStudio Remote Action
    global Object call(String action, Map<String, Object> args) {
        Map<String, Object> input = (Map<String, Object>) args.get('input');
        Map<String, Object> output = (Map<String, Object>) args.get('output');
        Map<String, Object> options = (Map<String, Object>) args.get('options');

        Object result = invokeMethod(action, input, output);
        System.debug('///Result: ' + result);

        return result;
    }

    // Dispatcher method to route requested actions
    public Boolean invokeMethod(String methodName, Map<String, Object> inputs, Map<String, Object> output) {
        Boolean result = true;
        try {
            if (methodName == 'encryptData') {
                String plainText = (String) inputs.get('plainText');
                String encrypted = encryptData(plainText);
                output.put('encryptedText', encrypted);
            } else if (methodName == 'decryptData') {
                String encryptedBase64 = (String) inputs.get('encryptedBase64');
                String decrypted = decryptData(encryptedBase64);
                output.put('decryptedText', decrypted);
            } 
             else {
                output.put('errorMessage', 'Unrecognized method: ' + methodName);
                result = false;
            }
        } catch (Exception e) {
            output.put('errorMessage', 'An unexpected error occurred.');
            UniLogger.writeERROR('Exception in UniScraicaDocumentoController', e);
            result = false;
        }
        return result;
    }

    // Encrypts plain text using AES128 and returns a Base64-encoded string
    public static String encryptData(String plainText) {
        try {
            UniKeysManager__c settings = UniKeysManager__c.getInstance();
            String base64SecretKey = settings.SecretKey__c;
            Blob key = EncodingUtil.base64Decode(base64SecretKey);
            Blob data = Blob.valueOf(plainText);
            Blob encrypted = Crypto.encryptWithManagedIV('AES256', key, data);
            return EncodingUtil.base64Encode(encrypted);
        } catch (Exception exc) {
            UniLogger.writeERROR('Encryption error', exc);
            return null;
        }
    }

    // Decrypts a Base64-encoded string using AES128 and returns plain text
    public static String decryptData(String encryptedBase64) {
        try {
            UniKeysManager__c settings = UniKeysManager__c.getInstance();
            String base64Key = settings.SecretKey__c;
            Blob key = EncodingUtil.base64Decode(base64Key);
            Blob encrypted = EncodingUtil.base64Decode(encryptedBase64);
            Blob decrypted = Crypto.decryptWithManagedIV('AES256', key, encrypted);
            return decrypted.toString();

        } catch (Exception exc) {
            UniLogger.writeERROR('Decryption error', exc);
            return null;
        }
    }
}