<?xml version="1.0" encoding="UTF-8"?>
<EntitlementProcess xmlns="http://soap.sforce.com/2006/04/metadata">
    <SObjectType>Case</SObjectType>
    <active>true</active>
    <businessHours>Unipol Rental CS - Orari di lavoro</businessHours>
    <entryStartDateField>Case.CreatedDate</entryStartDateField>
    <exitCriteriaFilterItems>
        <field>Case.IsClosed</field>
        <operation>equals</operation>
        <value>true</value>
    </exitCriteriaFilterItems>
    <milestones>
        <criteriaBooleanFilter>1 AND 2 AND 3 AND (4 AND 5) AND (6 AND 7) AND (8 AND 9) AND (10 AND 11) AND (12 AND 13) AND (14 AND 15) AND (16 AND 17) AND (18 AND 19) AND (20 AND 21)</criteriaBooleanFilter>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notEqual</operation>
            <value>Richiesta autorizzazione rent</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Supporto Area Riservata</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Problemi recupero credenziali AR,Errori in accesso AR,Problemi gestione ruoli driver,Problemi recupero documenti su AR,Errori applicativi AR,Verifica dati mancanti AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Chiusura Contratto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Danni rientro-Contestazione Fattura,Conguaglio km Contestazione Fattura,Elementi mancanti al rientro - Cont. Fattura,Auto restituita - Ritardo interruzione fatturazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Nuovo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Preassegnazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Sostitutiva</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Manutenzione Veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Tempistiche di riparazione,Contestazione riaddebito riparazione,Qualità Riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Ritardo sostituzione pneumatici,Sostituzione pneumatici non soddisfacente,Ritardo trasferimento su fornitore,Pneumatici -Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Sinistro - carrozzeria</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Tempi di riparazione,Contestazione riaddebito Penali,Qualità riparazione,Mancata autorizzazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Soccorso Stradale</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Ritardo,Mancato Soccorso,Mancata erogazione veicolo sostitutivo</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Goal</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneGoal</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
    <milestones>
        <criteriaBooleanFilter>1 AND 2 AND (3 AND 4) AND (5 AND 6) AND (7 AND 8) AND (9 AND 10) AND (11 AND 12) AND (13 AND 14) AND (15 AND 16) AND (17 AND 18) AND (19 AND 20)</criteriaBooleanFilter>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Risolto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Status</field>
            <operation>notEqual</operation>
            <value>Chiuso - Annullato</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Supporto Area Riservata</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Problemi recupero credenziali AR,Errori in accesso AR,Problemi gestione ruoli driver,Problemi recupero documenti su AR,Errori applicativi AR,Verifica dati mancanti AR</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Chiusura Contratto</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Danni rientro-Contestazione Fattura,Conguaglio km Contestazione Fattura,Elementi mancanti al rientro - Cont. Fattura,Auto restituita - Ritardo interruzione fatturazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Nuovo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Preassegnazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Consegna Veicolo Sostitutiva</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Mancata consegna,Ritardo consegna,Categoria Inferiore,Condizioni veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Manutenzione Veicolo</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Tempistiche di riparazione,Contestazione riaddebito riparazione,Qualità Riparazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Pneumatici</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Ritardo sostituzione pneumatici,Sostituzione pneumatici non soddisfacente,Ritardo trasferimento su fornitore,Pneumatici -Contestazione Fattura</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Sinistro - carrozzeria</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Tempi di riparazione,Contestazione riaddebito Penali,Qualità riparazione,Mancata autorizzazione</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.Categoria__c</field>
            <operation>equals</operation>
            <value>Reclamo Soccorso Stradale</value>
        </milestoneCriteriaFilterItems>
        <milestoneCriteriaFilterItems>
            <field>Case.SottoCategoria__c</field>
            <operation>notContain</operation>
            <value>Ritardo,Mancato Soccorso,Mancata erogazione veicolo sostitutivo</value>
        </milestoneCriteriaFilterItems>
        <milestoneName>Deadline</milestoneName>
        <minutesCustomClass>urcs_CalculateTimeCaseSLA</minutesCustomClass>
        <timeTriggers>
            <actions>
                <name>violazioneDeadline</name>
                <type>FieldUpdate</type>
            </actions>
            <timeLength>0</timeLength>
            <workflowTimeTriggerUnit>Minutes</workflowTimeTriggerUnit>
        </timeTriggers>
        <useCriteriaStartTime>true</useCriteriaStartTime>
    </milestones>
</EntitlementProcess>
