public with sharing class MenuStrumentiFei {
    
    public static String getFeiParams(string feiid, string userContext) {

        String payload = null;
        try {
            payload = [SELECT Id, sendFEIRequest_Payload__c FROM FEI_Settings__mdt 
                            WHERE Label =: feiId AND Environment__c =: FEI_Environment__c.getInstance().Environment__c LIMIT 1].sendFEIRequest_Payload__c;
        } catch(Exception e) {
            System.debug(e);
        }

        if(payload == null) {
            return payload;
        }

        String jsonParam = null;
        if(feiid == 'PREVENTIVATORE.UNISALUTE') {
            Map<String, Object> mapObj = (Map<String, Object>) JSON.deserializeUntyped(payload);
            
            String azienda = [SELECT IdAzienda__c FROM User WHERE Id = :UserInfo.getUserId()].IdAzienda__c;
            String externalId = null;
            if(azienda != null) {
                try {
                    Account acc = [SELECT ExternalId__c FROM Account WHERE Id = :azienda];
                    
                    if(acc != null && acc.ExternalId__c != null) {
                        externalId = acc.ExternalId__c.replace('AGE_', '');
                    }
                } catch(Exception e) {
                    System.debug(e);
                }
            }

            mapObj.put('userId', userContext);
            mapObj.put('agem', externalId);
            mapObj.put('agef', externalId);

            jsonParam = JSON.serialize(mapObj);
        }

        return jsonParam;
    }
}