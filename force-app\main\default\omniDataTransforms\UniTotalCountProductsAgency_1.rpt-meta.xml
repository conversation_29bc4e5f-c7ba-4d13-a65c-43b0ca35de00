<?xml version="1.0" encoding="UTF-8"?>
<OmniDataTransform xmlns="http://soap.sforce.com/2006/04/metadata">
    <active>true</active>
    <assignmentRulesUsed>false</assignmentRulesUsed>
    <deletedOnSuccess>false</deletedOnSuccess>
    <errorIgnored>false</errorIgnored>
    <expectedInputJson>{
  &quot;checkCp&quot; : {
    &quot;error&quot; : &quot;OK&quot;,
    &quot;allPermission&quot; : false,
    &quot;errorCode&quot; : &quot;INVOKE-200&quot;,
    &quot;anyPermission&quot; : false,
    &quot;customerPermissionCheck&quot; : {
      &quot;X182_202030000&quot; : false,
      &quot;X182_202200200&quot; : false,
      &quot;X172_201030300&quot; : false,
      &quot;X172_201030700&quot; : false,
      &quot;X172_201030500&quot; : false,
      &quot;X172_201030900&quot; : false,
      &quot;X172_202150200&quot; : false,
      &quot;X172_202020102&quot; : false,
      &quot;X172_202020103&quot; : false,
      &quot;X172_202150000&quot; : false,
      &quot;X172_202020100&quot; : false,
      &quot;X172_202020101&quot; : false,
      &quot;X182_202010000&quot; : false,
      &quot;X182_206110100&quot; : false,
      &quot;X182_202210100&quot; : false,
      &quot;X182_201020000&quot; : false,
      &quot;X174_202000000&quot; : false,
      &quot;X172_202150600&quot; : false,
      &quot;X172_202020502&quot; : false,
      &quot;X172_202020503&quot; : false,
      &quot;X172_202150400&quot; : false,
      &quot;X172_202020500&quot; : false,
      &quot;X172_202020104&quot; : false,
      &quot;X172_202020501&quot; : false,
      &quot;X172_202020105&quot; : false,
      &quot;X172_202020504&quot; : false,
      &quot;X182_201010000&quot; : false,
      &quot;X182_202200100&quot; : false,
      &quot;X182_202120000&quot; : false,
      &quot;X172_201030400&quot; : false,
      &quot;X172_201030200&quot; : false,
      &quot;X172_201030800&quot; : false,
      &quot;X172_201030600&quot; : false,
      &quot;X172_202020000&quot; : false,
      &quot;X172_202150101&quot; : false,
      &quot;X172_202150100&quot; : false,
      &quot;X172_201031000&quot; : false,
      &quot;X174_201000000&quot; : false,
      &quot;X169_506000000&quot; : false,
      &quot;X182_202140000&quot; : false,
      &quot;X182_202210200&quot; : false,
      &quot;X182_206110200&quot; : false,
      &quot;X172_202150501&quot; : false,
      &quot;X172_202150303&quot; : false,
      &quot;X172_202150105&quot; : false,
      &quot;X172_202150500&quot; : false,
      &quot;X172_202150302&quot; : false,
      &quot;X172_202150104&quot; : false,
      &quot;X172_202150301&quot; : false,
      &quot;X172_202150103&quot; : false,
      &quot;X172_202150300&quot; : false,
      &quot;X172_202150102&quot; : false,
      &quot;X172_202020600&quot; : false,
      &quot;X172_202150504&quot; : false,
      &quot;X172_202150503&quot; : false,
      &quot;X172_202150305&quot; : false,
      &quot;X172_202150107&quot; : false,
      &quot;X172_202150502&quot; : false,
      &quot;X172_202150304&quot; : false,
      &quot;X172_202150106&quot; : false
    }
  },
  &quot;prodottiAssicurativi&quot; : {
    &quot;has_unica_policies&quot; : true,
    &quot;same_agency&quot; : {
      &quot;salute&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;casa_famiglia&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;vita&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_count&quot; : 0,
      &quot;persona&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;motor&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_premium_amount&quot; : &quot;€0,00&quot;
    },
    &quot;isAbbinato&quot; : false,
    &quot;other_agency&quot; : {
      &quot;salute&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;casa_famiglia&quot; : {
        &quot;count&quot; : 3,
        &quot;premium_amount&quot; : &quot;€240,88&quot;
      },
      &quot;vita&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_count&quot; : 7,
      &quot;persona&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;motor&quot; : {
        &quot;count&quot; : 4,
        &quot;premium_amount&quot; : &quot;€2.130,79&quot;
      },
      &quot;total_premium_amount&quot; : &quot;€2.371,67&quot;
    }
  }
}</expectedInputJson>
    <fieldLevelSecurityEnabled>false</fieldLevelSecurityEnabled>
    <inputType>JSON</inputType>
    <name>UniTotalCountProductsAgency</name>
    <nullInputsIncludedInOutput>false</nullInputsIncludedInOutput>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniTotalCountProductsAgencyCustom911</globalKey>
        <inputFieldName>prodottiAssicurativi:other_agency:total_count</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniTotalCountProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>other_agency_policies_count</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <omniDataTransformItem>
        <disabled>false</disabled>
        <filterGroup>0.0</filterGroup>
        <globalKey>UniTotalCountProductsAgencyCustom8997</globalKey>
        <inputFieldName>prodottiAssicurativi:same_agency:total_count</inputFieldName>
        <inputObjectQuerySequence>0.0</inputObjectQuerySequence>
        <linkedObjectSequence>0.0</linkedObjectSequence>
        <name>UniTotalCountProductsAgency</name>
        <outputCreationSequence>1.0</outputCreationSequence>
        <outputFieldName>same_agency_policies_count</outputFieldName>
        <outputObjectName>json</outputObjectName>
        <requiredForUpsert>false</requiredForUpsert>
        <upsertKey>false</upsertKey>
    </omniDataTransformItem>
    <outputType>JSON</outputType>
    <previewJsonData>{
  &quot;checkCp&quot; : {
    &quot;error&quot; : &quot;OK&quot;,
    &quot;allPermission&quot; : false,
    &quot;errorCode&quot; : &quot;INVOKE-200&quot;,
    &quot;anyPermission&quot; : false,
    &quot;customerPermissionCheck&quot; : {
      &quot;X182_202030000&quot; : false,
      &quot;X182_202200200&quot; : false,
      &quot;X172_201030300&quot; : false,
      &quot;X172_201030700&quot; : false,
      &quot;X172_201030500&quot; : false,
      &quot;X172_201030900&quot; : false,
      &quot;X172_202150200&quot; : false,
      &quot;X172_202020102&quot; : false,
      &quot;X172_202020103&quot; : false,
      &quot;X172_202150000&quot; : false,
      &quot;X172_202020100&quot; : false,
      &quot;X172_202020101&quot; : false,
      &quot;X182_202010000&quot; : false,
      &quot;X182_206110100&quot; : false,
      &quot;X182_202210100&quot; : false,
      &quot;X182_201020000&quot; : false,
      &quot;X174_202000000&quot; : false,
      &quot;X172_202150600&quot; : false,
      &quot;X172_202020502&quot; : false,
      &quot;X172_202020503&quot; : false,
      &quot;X172_202150400&quot; : false,
      &quot;X172_202020500&quot; : false,
      &quot;X172_202020104&quot; : false,
      &quot;X172_202020501&quot; : false,
      &quot;X172_202020105&quot; : false,
      &quot;X172_202020504&quot; : false,
      &quot;X182_201010000&quot; : false,
      &quot;X182_202200100&quot; : false,
      &quot;X182_202120000&quot; : false,
      &quot;X172_201030400&quot; : false,
      &quot;X172_201030200&quot; : false,
      &quot;X172_201030800&quot; : false,
      &quot;X172_201030600&quot; : false,
      &quot;X172_202020000&quot; : false,
      &quot;X172_202150101&quot; : false,
      &quot;X172_202150100&quot; : false,
      &quot;X172_201031000&quot; : false,
      &quot;X174_201000000&quot; : false,
      &quot;X169_506000000&quot; : false,
      &quot;X182_202140000&quot; : false,
      &quot;X182_202210200&quot; : false,
      &quot;X182_206110200&quot; : false,
      &quot;X172_202150501&quot; : false,
      &quot;X172_202150303&quot; : false,
      &quot;X172_202150105&quot; : false,
      &quot;X172_202150500&quot; : false,
      &quot;X172_202150302&quot; : false,
      &quot;X172_202150104&quot; : false,
      &quot;X172_202150301&quot; : false,
      &quot;X172_202150103&quot; : false,
      &quot;X172_202150300&quot; : false,
      &quot;X172_202150102&quot; : false,
      &quot;X172_202020600&quot; : false,
      &quot;X172_202150504&quot; : false,
      &quot;X172_202150503&quot; : false,
      &quot;X172_202150305&quot; : false,
      &quot;X172_202150107&quot; : false,
      &quot;X172_202150502&quot; : false,
      &quot;X172_202150304&quot; : false,
      &quot;X172_202150106&quot; : false
    }
  },
  &quot;prodottiAssicurativi&quot; : {
    &quot;has_unica_policies&quot; : true,
    &quot;same_agency&quot; : {
      &quot;salute&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;casa_famiglia&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;vita&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_count&quot; : 0,
      &quot;persona&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;motor&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_premium_amount&quot; : &quot;€0,00&quot;
    },
    &quot;isAbbinato&quot; : false,
    &quot;other_agency&quot; : {
      &quot;salute&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;casa_famiglia&quot; : {
        &quot;count&quot; : 3,
        &quot;premium_amount&quot; : &quot;€240,88&quot;
      },
      &quot;vita&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;total_count&quot; : 7,
      &quot;persona&quot; : {
        &quot;count&quot; : 0,
        &quot;premium_amount&quot; : &quot;€0,00&quot;
      },
      &quot;motor&quot; : {
        &quot;count&quot; : 4,
        &quot;premium_amount&quot; : &quot;€2.130,79&quot;
      },
      &quot;total_premium_amount&quot; : &quot;€2.371,67&quot;
    }
  }
}</previewJsonData>
    <processSuperBulk>false</processSuperBulk>
    <responseCacheTtlMinutes>0.0</responseCacheTtlMinutes>
    <rollbackOnError>false</rollbackOnError>
    <sourceObject>json</sourceObject>
    <sourceObjectDefault>false</sourceObjectDefault>
    <synchronousProcessThreshold>0.0</synchronousProcessThreshold>
    <type>Transform</type>
    <uniqueName>UniTotalCountProductsAgency_1</uniqueName>
    <versionNumber>1.0</versionNumber>
    <xmlDeclarationRemoved>false</xmlDeclarationRemoved>
</OmniDataTransform>
