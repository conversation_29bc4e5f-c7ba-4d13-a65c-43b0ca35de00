# Case Field History Tracking - Implementazione

## Panoramica
È stato implementato il tracking dello storico delle modifiche per i campi critici del Case, permettendo agli operatori Unipol Rental di monitorare le modifiche apportate e garantire la trasparenza delle informazioni.

## Campi Tracciati

I seguenti campi del Case hanno ora abilitato il field history tracking:

### 1. **Status** (Stato)
- **Campo**: Status
- **Descrizione**: Registra le modifiche dello stato per monitorare gli avanzamenti nella gestione del Case
- **Valori tracciati**: Tutti i valori dello stato del Case (Nuova richiesta, In gestione, Trasferito, ecc.)

### 2. **Owner** (Titolare caso)
- **Campo**: OwnerId
- **Descrizione**: Registra le modifiche del titolare quando il case viene trasferito a un altro ufficio
- **Valori tracciati**: Utenti e code di assegnazione

### 3. **Utente Assegnatario**
- **Campo**: UtAssegnatario__c
- **Descrizione**: Registra le modifiche dell'utente assegnatario quando un utente prende in carico il case
- **Valori tracciati**: Riferimenti agli utenti assegnatari

### 4. **Zona di circolazione**
- **Campo**: Zona_di_circolazione__c
- **Descrizione**: Registra le modifiche della zona di circolazione per tenere traccia delle variazioni nell'area di riferimento
- **Valori tracciati**: Codici e nomi delle province italiane (AG-Agrigento, AL-Alessandria, ecc.)

## Come Accedere allo Storico

### Accesso dalla UI
1. Aprire il record del Case
2. Scorrere verso il basso fino alla sezione **"History Tracking"**
3. Visualizzare l'elenco delle modifiche con:
   - Valore precedente del campo
   - Nuovo valore del campo
   - Data e ora della modifica
   - Utente che ha effettuato la modifica

### Informazioni Visualizzate
Per ogni modifica tracciata, lo storico include:
- **Campo modificato**: Nome del campo che è stato cambiato
- **Valore precedente**: Il valore che il campo aveva prima della modifica
- **Nuovo valore**: Il valore assegnato al campo dopo la modifica
- **Data/Ora**: Timestamp preciso della modifica
- **Utente**: Nome dell'utente che ha effettuato la modifica

## Retention Policy

### Periodo di Conservazione
- **Interfaccia Utente**: 18 mesi
- **API**: 24 mesi

Questi sono i limiti standard di Salesforce per il field history tracking e soddisfano i requisiti AC4.

### Accesso via API
Per accedere ai dati storici oltre i 18 mesi (fino a 24 mesi), utilizzare:
- Data Loader con queryAll()
- REST API o SOAP API con query sui record CaseHistory

## Note Tecniche

### File di Configurazione Modificati
1. `force-app/main/default/objects/Case/Case.object-meta.xml`
   - Aggiunto `<enableHistory>true</enableHistory>`

2. `force-app/main/default/objects/Case/fields/Status.field-meta.xml`
   - Nuovo file con `<trackHistory>true</trackHistory>`

3. `force-app/main/default/objects/Case/fields/OwnerId.field-meta.xml`
   - Nuovo file con `<trackHistory>true</trackHistory>`

4. `force-app/rental/default/objects/Case/fields/UtAssegnatario__c.field-meta.xml`
   - Modificato `<trackHistory>false</trackHistory>` in `<trackHistory>true</trackHistory>`

5. `force-app/rental/default/objects/Case/fields/Zona_di_circolazione__c.field-meta.xml`
   - Modificato `<trackHistory>false</trackHistory>` in `<trackHistory>true</trackHistory>`

### Deployment
Dopo il deployment di questi metadati, il field history tracking sarà attivo per tutti i nuovi cambiamenti. Le modifiche precedenti al deployment non saranno visibili nello storico.

## Conformità ai Requisiti

✅ **AC1**: Tracking abilitato per tutti i 4 campi richiesti
✅ **AC2**: Storico include valore precedente, nuovo valore, data/ora e utente
✅ **AC3**: Accessibile nella sezione History Tracking del case
✅ **AC4**: Retention di 18 mesi (UI) e 24 mesi (API)
